---
marp: true
theme: default
paginate: true
header: 'Python Functions - CS Course'
footer: 'University Computer Science Department'
---

# Python Functions
## A Comprehensive Guide for CS Students

---

## What is a Function?

A **function** is a reusable block of code that performs a specific task.

**Benefits:**
- Code reusability
- Better organization
- Easier debugging
- Modular programming

---

## Basic Function Syntax

```python
def function_name(parameters):
    """Optional docstring"""
    # Function body
    return value  # Optional
```

**Key components:**
- `def` keyword
- Function name
- Parameters (optional)
- Docstring (optional but recommended)
- Function body
- Return statement (optional)

---

## Simple Function Example

```python
def greet():
    """A simple greeting function"""
    print("Hello, <PERSON>!")

# Function call
greet()
```

**Output:**
```
Hello, <PERSON>!
```

---

## Functions with Parameters

```python
def greet_person(name):
    """Greet a specific person"""
    print(f"Hello, {name}!")

# Function calls
greet_person("<PERSON>")
greet_person("<PERSON>")
```

**Output:**
```
Hello, <PERSON>!
Hello, <PERSON>!
```

---

## Multiple Parameters

```python
def add_numbers(a, b):
    """Add two numbers together"""
    result = a + b
    print(f"{a} + {b} = {result}")

# Function call
add_numbers(5, 3)
add_numbers(10, 7)
```

**Output:**
```
5 + 3 = 8
10 + 7 = 17
```

---

## Return Values

```python
def multiply(x, y):
    """Multiply two numbers and return result"""
    return x * y

# Using return value
result = multiply(4, 6)
print(f"Result: {result}")

# Direct use in expression
total = multiply(3, 5) + multiply(2, 4)
print(f"Total: {total}")
```

**Output:**
```
Result: 24
Total: 23
```

---

## Default Parameters

```python
def introduce(name, age=18, city="Unknown"):
    """Introduce a person with default values"""
    print(f"Name: {name}")
    print(f"Age: {age}")
    print(f"City: {city}")

# Different ways to call
introduce("Alice")
introduce("Bob", 25)
introduce("Charlie", 30, "New York")
```

---

## Keyword Arguments

```python
def create_profile(name, age, email, city):
    """Create user profile"""
    return {
        'name': name,
        'age': age,
        'email': email,
        'city': city
    }

# Using keyword arguments
profile = create_profile(
    name="Alice",
    city="Boston",
    age=25,
    email="<EMAIL>"
)
```

---

## Variable-Length Arguments (*args)

```python
def sum_all(*numbers):
    """Sum any number of arguments"""
    total = 0
    for num in numbers:
        total += num
    return total

# Different number of arguments
print(sum_all(1, 2, 3))           # 6
print(sum_all(1, 2, 3, 4, 5))     # 15
print(sum_all(10))                # 10
```

---

## Keyword Variable Arguments (**kwargs)

```python
def print_info(**info):
    """Print key-value information"""
    for key, value in info.items():
        print(f"{key}: {value}")

# Using **kwargs
print_info(
    name="Alice",
    age=25,
    job="Engineer",
    city="Boston"
)
```

---

## Combining *args and **kwargs

```python
def flexible_function(required, *args, **kwargs):
    """Function with all parameter types"""
    print(f"Required: {required}")
    
    if args:
        print(f"Args: {args}")
    
    if kwargs:
        print(f"Kwargs: {kwargs}")

# Example call
flexible_function("Hello", 1, 2, 3, name="Alice", age=25)
```

---

## Local vs Global Scope

```python
global_var = "I'm global"

def scope_example():
    local_var = "I'm local"
    print(f"Inside function: {global_var}")
    print(f"Inside function: {local_var}")

scope_example()
print(f"Outside function: {global_var}")
# print(local_var)  # This would cause an error
```

---

## Global Keyword

```python
counter = 0  # Global variable

def increment():
    global counter
    counter += 1
    print(f"Counter: {counter}")

increment()  # Counter: 1
increment()  # Counter: 2
print(f"Final counter: {counter}")  # Final counter: 2
```

---

## Lambda Functions

```python
# Regular function
def square(x):
    return x ** 2

# Lambda equivalent
square_lambda = lambda x: x ** 2

# Usage
print(square(5))        # 25
print(square_lambda(5)) # 25

# Lambda with multiple parameters
add = lambda x, y: x + y
print(add(3, 4))        # 7
```

---

## Lambda with Built-in Functions

```python
numbers = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10]

# Filter even numbers
evens = list(filter(lambda x: x % 2 == 0, numbers))
print(f"Even numbers: {evens}")

# Square all numbers
squares = list(map(lambda x: x ** 2, numbers))
print(f"Squares: {squares[:5]}...")  # Show first 5
```

---

## Recursive Functions

```python
def factorial(n):
    """Calculate factorial recursively"""
    # Base case
    if n == 0 or n == 1:
        return 1
    
    # Recursive case
    return n * factorial(n - 1)

# Examples
print(f"5! = {factorial(5)}")   # 120
print(f"3! = {factorial(3)}")   # 6
```

---

## Fibonacci with Recursion

```python
def fibonacci(n):
    """Generate nth Fibonacci number"""
    if n <= 1:
        return n
    
    return fibonacci(n - 1) + fibonacci(n - 2)

# Generate first 10 Fibonacci numbers
fib_sequence = [fibonacci(i) for i in range(10)]
print(f"Fibonacci: {fib_sequence}")
```

---

## Docstrings and Documentation

```python
def calculate_area(length, width):
    """
    Calculate the area of a rectangle.
    
    Args:
        length (float): The length of the rectangle
        width (float): The width of the rectangle
    
    Returns:
        float: The area of the rectangle
    
    Example:
        >>> calculate_area(5, 3)
        15
    """
    return length * width
```

---

## Function Annotations

```python
def greet_user(name: str, age: int) -> str:
    """
    Greet a user with type hints.
    
    Args:
        name: User's name
        age: User's age
    
    Returns:
        Greeting message
    """
    return f"Hello {name}, you are {age} years old!"

# Usage
message = greet_user("Alice", 25)
print(message)
```

---

## Higher-Order Functions

```python
def apply_operation(numbers, operation):
    """Apply an operation to a list of numbers"""
    return [operation(num) for num in numbers]

def double(x):
    return x * 2

def square(x):
    return x ** 2

# Usage
nums = [1, 2, 3, 4, 5]
doubled = apply_operation(nums, double)
squared = apply_operation(nums, square)

print(f"Doubled: {doubled}")
print(f"Squared: {squared}")
```

---

## Decorators Introduction

```python
def my_decorator(func):
    """A simple decorator"""
    def wrapper():
        print("Before function call")
        func()
        print("After function call")
    return wrapper

@my_decorator
def say_hello():
    print("Hello!")

# Usage
say_hello()
```

**Output:**
```
Before function call
Hello!
After function call
```

---

## Best Practices

1. **Use descriptive names**
   ```python
   # Good
   def calculate_tax(income, rate):
       return income * rate
   
   # Avoid
   def calc(x, y):
       return x * y
   ```

2. **Keep functions small and focused**
3. **Use docstrings**
4. **Handle edge cases**
5. **Use type hints when helpful**

---

## Common Mistakes to Avoid

1. **Modifying mutable default arguments**
   ```python
   # Wrong
   def add_item(item, target_list=[]):
       target_list.append(item)
       return target_list
   
   # Correct
   def add_item(item, target_list=None):
       if target_list is None:
           target_list = []
       target_list.append(item)
       return target_list
   ```

---

## Practice Exercise

**Create a function that:**
1. Takes a list of numbers
2. Filters out negative numbers
3. Squares the remaining numbers
4. Returns the sum

```python
def process_numbers(numbers):
    """Your implementation here"""
    pass

# Test with: [1, -2, 3, -4, 5]
# Expected result: 1² + 3² + 5² = 35
```

---

## Summary

**Key Concepts Covered:**
- Function definition and calling
- Parameters and arguments
- Return values
- Scope and global variables
- Lambda functions
- Recursion
- Documentation and type hints
- Best practices

**Next Steps:**
- Practice writing functions
- Explore advanced topics (decorators, generators)
- Apply functions in real projects

---

## Exercise Solution

```python
def process_numbers(numbers):
    """
    Process a list of numbers: filter negatives,
    square positives, return sum.
    """
    positive_numbers = [num for num in numbers if num > 0]
    squared_numbers = [num ** 2 for num in positive_numbers]
    return sum(squared_numbers)

# Test
test_list = [1, -2, 3, -4, 5]
result = process_numbers(test_list)
print(f"Result: {result}")  # Output: 35
```

---

## Function Parameters Deep Dive

**Parameter Types:**
1. **Positional parameters** - order matters
2. **Keyword parameters** - name matters
3. **Default parameters** - have default values
4. **Variable-length parameters** - *args, **kwargs

---

## Positional vs Keyword Arguments

```python
def book_info(title, author, year, genre="Fiction"):
    print(f"Title: {title}")
    print(f"Author: {author}")
    print(f"Year: {year}")
    print(f"Genre: {genre}")

# Positional arguments
book_info("1984", "George Orwell", 1949)

# Mixed positional and keyword
book_info("Dune", "Frank Herbert", year=1965, genre="Sci-Fi")
```

---

## Parameter Order Rules

```python
def function_example(pos_only, /, standard, *, kwd_only, **kwargs):
    """
    Parameter order must be:
    1. Positional-only (before /)
    2. Standard parameters
    3. Keyword-only (after *)
    4. **kwargs
    """
    pass

# Valid calls
function_example("pos", "std", kwd_only="kwd", extra="value")
```

---

## Unpacking Arguments

```python
def greet_three(first, second, third):
    print(f"Hello {first}, {second}, and {third}!")

# Unpacking a list
names = ["Alice", "Bob", "Charlie"]
greet_three(*names)

# Unpacking a dictionary
person = {"first": "Alice", "second": "Bob", "third": "Charlie"}
greet_three(**person)
```

---

## Function Return Types

**Functions can return:**
- Single values
- Multiple values (tuples)
- Collections (lists, dicts)
- Other functions
- Nothing (None)

---

## Multiple Return Values

```python
def get_name_parts(full_name):
    """Split full name into parts"""
    parts = full_name.split()
    first = parts[0]
    last = parts[-1]
    middle = " ".join(parts[1:-1]) if len(parts) > 2 else ""

    return first, middle, last

# Unpacking return values
first, middle, last = get_name_parts("John Michael Smith")
print(f"First: {first}, Middle: {middle}, Last: {last}")
```

---

## Returning Functions

```python
def create_multiplier(factor):
    """Return a function that multiplies by factor"""
    def multiplier(number):
        return number * factor

    return multiplier

# Create specific multipliers
double = create_multiplier(2)
triple = create_multiplier(3)

print(double(5))  # 10
print(triple(4))  # 12
```

---

## Function Scope Deep Dive

**Scope Resolution Order (LEGB):**
1. **Local** - inside function
2. **Enclosing** - in enclosing function
3. **Global** - module level
4. **Built-in** - built-in names

---

## LEGB Example

```python
x = "global"

def outer():
    x = "enclosing"

    def inner():
        x = "local"
        print(f"Inner x: {x}")

    inner()
    print(f"Outer x: {x}")

outer()
print(f"Global x: {x}")
```

**Output:**
```
Inner x: local
Outer x: enclosing
Global x: global
```

---

## Nonlocal Keyword

```python
def outer():
    count = 0

    def inner():
        nonlocal count
        count += 1
        return count

    return inner

# Usage
counter = outer()
print(counter())  # 1
print(counter())  # 2
print(counter())  # 3
```

---

## Closures

```python
def make_counter(start=0):
    """Create a counter function with closure"""
    count = start

    def counter():
        nonlocal count
        count += 1
        return count

    return counter

# Each counter maintains its own state
counter1 = make_counter()
counter2 = make_counter(10)

print(counter1())  # 1
print(counter2())  # 11
print(counter1())  # 2
```

---

## Advanced Lambda Functions

```python
# Lambda with conditional
max_func = lambda a, b: a if a > b else b
print(max_func(5, 3))  # 5

# Lambda for sorting
students = [("Alice", 85), ("Bob", 90), ("Charlie", 78)]
students.sort(key=lambda student: student[1])
print(students)  # Sorted by grade
```

---

## Lambda Limitations

**Lambda functions:**
- Can only contain expressions, not statements
- Cannot contain assignments
- Cannot contain loops or conditionals (except ternary)
- Limited to single line

```python
# This won't work in lambda
def complex_function(x):
    if x > 0:
        result = x * 2
    else:
        result = x * -1
    return result
```

---

## Map, Filter, Reduce

```python
from functools import reduce

numbers = [1, 2, 3, 4, 5]

# Map - transform each element
squares = list(map(lambda x: x**2, numbers))
print(f"Squares: {squares}")

# Filter - select elements
evens = list(filter(lambda x: x % 2 == 0, numbers))
print(f"Evens: {evens}")

# Reduce - combine elements
product = reduce(lambda x, y: x * y, numbers)
print(f"Product: {product}")
```

---

## Recursion Fundamentals

**Every recursive function needs:**
1. **Base case** - condition to stop recursion
2. **Recursive case** - function calls itself
3. **Progress** - moves toward base case

---

## Recursion vs Iteration

```python
# Recursive factorial
def factorial_recursive(n):
    if n <= 1:
        return 1
    return n * factorial_recursive(n - 1)

# Iterative factorial
def factorial_iterative(n):
    result = 1
    for i in range(1, n + 1):
        result *= i
    return result

# Both produce same result
print(factorial_recursive(5))  # 120
print(factorial_iterative(5))  # 120
```

---

## Tree Traversal with Recursion

```python
def print_directory(path, level=0):
    """Recursively print directory structure"""
    import os

    indent = "  " * level
    print(f"{indent}{os.path.basename(path)}/")

    try:
        for item in os.listdir(path):
            item_path = os.path.join(path, item)
            if os.path.isdir(item_path):
                print_directory(item_path, level + 1)
            else:
                print(f"{indent}  {item}")
    except PermissionError:
        print(f"{indent}  [Permission Denied]")
```

---

## Memoization

```python
def fibonacci_memo(n, memo={}):
    """Fibonacci with memoization for efficiency"""
    if n in memo:
        return memo[n]

    if n <= 1:
        return n

    memo[n] = fibonacci_memo(n-1, memo) + fibonacci_memo(n-2, memo)
    return memo[n]

# Much faster for large numbers
print(fibonacci_memo(50))
```

---

## Decorators Explained

**A decorator:**
- Is a function that takes another function as argument
- Returns a modified version of that function
- Uses the `@` syntax for clean application

---

## Simple Decorator Example

```python
def timer_decorator(func):
    """Measure function execution time"""
    import time

    def wrapper(*args, **kwargs):
        start_time = time.time()
        result = func(*args, **kwargs)
        end_time = time.time()
        print(f"{func.__name__} took {end_time - start_time:.4f} seconds")
        return result

    return wrapper

@timer_decorator
def slow_function():
    import time
    time.sleep(1)
    return "Done!"
```

---

## Decorator with Parameters

```python
def repeat(times):
    """Decorator that repeats function execution"""
    def decorator(func):
        def wrapper(*args, **kwargs):
            for _ in range(times):
                result = func(*args, **kwargs)
            return result
        return wrapper
    return decorator

@repeat(3)
def greet(name):
    print(f"Hello, {name}!")

greet("Alice")  # Prints greeting 3 times
```

---

## Built-in Decorators

```python
class MyClass:
    def __init__(self):
        self._value = 0

    @property
    def value(self):
        """Getter method"""
        return self._value

    @value.setter
    def value(self, new_value):
        """Setter method with validation"""
        if new_value < 0:
            raise ValueError("Value must be non-negative")
        self._value = new_value

# Usage
obj = MyClass()
obj.value = 10  # Uses setter
print(obj.value)  # Uses getter
```

---

## Function Annotations Deep Dive

```python
from typing import List, Dict, Optional, Union

def process_data(
    data: List[Dict[str, Union[str, int]]],
    filter_key: str,
    default_value: Optional[str] = None
) -> List[str]:
    """
    Process a list of dictionaries with type hints.

    Args:
        data: List of dictionaries containing mixed types
        filter_key: Key to extract from each dictionary
        default_value: Default value if key not found

    Returns:
        List of string values
    """
    result = []
    for item in data:
        value = item.get(filter_key, default_value)
        if value is not None:
            result.append(str(value))
    return result
```

---

## Error Handling in Functions

```python
def safe_divide(a, b):
    """Safely divide two numbers with error handling"""
    try:
        if not isinstance(a, (int, float)) or not isinstance(b, (int, float)):
            raise TypeError("Arguments must be numbers")

        if b == 0:
            raise ValueError("Cannot divide by zero")

        return a / b

    except TypeError as e:
        print(f"Type error: {e}")
        return None
    except ValueError as e:
        print(f"Value error: {e}")
        return None
```

---

## Custom Exceptions

```python
class InvalidAgeError(Exception):
    """Custom exception for invalid age values"""
    pass

def create_person(name, age):
    """Create person with age validation"""
    if not isinstance(age, int):
        raise TypeError("Age must be an integer")

    if age < 0:
        raise InvalidAgeError("Age cannot be negative")

    if age > 150:
        raise InvalidAgeError("Age seems unrealistic")

    return {"name": name, "age": age}

# Usage with error handling
try:
    person = create_person("Alice", -5)
except InvalidAgeError as e:
    print(f"Invalid age: {e}")
```

---

## Function Testing

```python
def add_numbers(a, b):
    """Add two numbers together"""
    return a + b

def test_add_numbers():
    """Test the add_numbers function"""
    # Test positive numbers
    assert add_numbers(2, 3) == 5

    # Test negative numbers
    assert add_numbers(-1, 1) == 0

    # Test zero
    assert add_numbers(0, 5) == 5

    # Test floats
    assert add_numbers(2.5, 1.5) == 4.0

    print("All tests passed!")

test_add_numbers()
```

---

## Doctest

```python
def calculate_circle_area(radius):
    """
    Calculate the area of a circle.

    Args:
        radius (float): The radius of the circle

    Returns:
        float: The area of the circle

    Examples:
        >>> calculate_circle_area(1)
        3.141592653589793
        >>> calculate_circle_area(2)
        12.566370614359172
        >>> round(calculate_circle_area(3), 2)
        28.27
    """
    import math
    return math.pi * radius ** 2

if __name__ == "__main__":
    import doctest
    doctest.testmod()
```

---

## Generator Functions

```python
def fibonacci_generator():
    """Generate Fibonacci numbers infinitely"""
    a, b = 0, 1
    while True:
        yield a
        a, b = b, a + b

# Usage
fib = fibonacci_generator()
for i in range(10):
    print(next(fib), end=" ")
# Output: 0 1 1 2 3 5 8 13 21 34
```

---

## Generator Expressions

```python
# Generator expression for squares
squares_gen = (x**2 for x in range(10))

# Memory efficient - generates values on demand
print(list(squares_gen))  # [0, 1, 4, 9, 16, 25, 36, 49, 64, 81]

# File processing with generators
def read_large_file(file_path):
    """Read large file line by line"""
    with open(file_path, 'r') as file:
        for line in file:
            yield line.strip()
```

---

## Partial Functions

```python
from functools import partial

def multiply(x, y, z):
    """Multiply three numbers"""
    return x * y * z

# Create partial functions
double = partial(multiply, 2)  # Fix first argument
triple_by_two = partial(multiply, y=2)  # Fix keyword argument

# Usage
print(double(3, 4))  # multiply(2, 3, 4) = 24
print(triple_by_two(5, z=3))  # multiply(5, y=2, z=3) = 30
```

---

## Function Caching

```python
from functools import lru_cache

@lru_cache(maxsize=128)
def expensive_function(n):
    """Simulate expensive computation with caching"""
    print(f"Computing for {n}")
    import time
    time.sleep(1)  # Simulate expensive operation
    return n ** 2

# First call - computes and caches
print(expensive_function(5))  # Takes ~1 second

# Second call - returns cached result
print(expensive_function(5))  # Returns immediately
```

---

## Async Functions (Introduction)

```python
import asyncio

async def fetch_data(url):
    """Simulate async data fetching"""
    print(f"Fetching data from {url}")
    await asyncio.sleep(1)  # Simulate network delay
    return f"Data from {url}"

async def main():
    """Main async function"""
    # Run multiple async operations concurrently
    tasks = [
        fetch_data("api1.com"),
        fetch_data("api2.com"),
        fetch_data("api3.com")
    ]

    results = await asyncio.gather(*tasks)
    for result in results:
        print(result)

# Run async code
# asyncio.run(main())
```

---

## Function Design Principles

**SOLID Principles for Functions:**

1. **Single Responsibility** - One function, one purpose
2. **Open/Closed** - Open for extension, closed for modification
3. **Liskov Substitution** - Functions should be replaceable
4. **Interface Segregation** - Small, focused interfaces
5. **Dependency Inversion** - Depend on abstractions

---

## Pure Functions

```python
# Pure function - no side effects, same input = same output
def add_pure(a, b):
    """Pure function - predictable and testable"""
    return a + b

# Impure function - has side effects
counter = 0
def add_impure(a, b):
    """Impure function - modifies global state"""
    global counter
    counter += 1
    print(f"Function called {counter} times")
    return a + b

# Pure functions are easier to test and reason about
```

---

## Function Composition

```python
def compose(f, g):
    """Compose two functions: f(g(x))"""
    return lambda x: f(g(x))

def add_one(x):
    return x + 1

def multiply_by_two(x):
    return x * 2

# Compose functions
add_then_multiply = compose(multiply_by_two, add_one)
multiply_then_add = compose(add_one, multiply_by_two)

print(add_then_multiply(5))    # (5 + 1) * 2 = 12
print(multiply_then_add(5))    # (5 * 2) + 1 = 11
```

---

## Monkey Patching

```python
class Calculator:
    def add(self, a, b):
        return a + b

# Original behavior
calc = Calculator()
print(calc.add(2, 3))  # 5

# Monkey patch - add new method
def multiply(self, a, b):
    return a * b

Calculator.multiply = multiply

# Now calculator has multiply method
print(calc.multiply(2, 3))  # 6
```

---

## Function Introspection

```python
def example_function(a, b=10, *args, **kwargs):
    """Example function for introspection"""
    return a + b

# Inspect function properties
print(f"Name: {example_function.__name__}")
print(f"Doc: {example_function.__doc__}")
print(f"Defaults: {example_function.__defaults__}")

# Get function signature
import inspect
sig = inspect.signature(example_function)
print(f"Signature: {sig}")

# Get parameter information
for param in sig.parameters.values():
    print(f"Parameter: {param.name}, Default: {param.default}")
```

---

## Performance Considerations

```python
import time

def time_function(func, *args, **kwargs):
    """Measure function execution time"""
    start = time.perf_counter()
    result = func(*args, **kwargs)
    end = time.perf_counter()
    print(f"{func.__name__}: {end - start:.6f} seconds")
    return result

# Compare list comprehension vs loop
def list_comp_approach(n):
    return [x**2 for x in range(n)]

def loop_approach(n):
    result = []
    for x in range(n):
        result.append(x**2)
    return result

# Test performance
n = 100000
time_function(list_comp_approach, n)
time_function(loop_approach, n)
```

---

## Memory Management

```python
import sys

def memory_usage_demo():
    """Demonstrate memory usage of different approaches"""

    # Generator - memory efficient
    def squares_generator(n):
        for i in range(n):
            yield i**2

    # List - memory intensive
    def squares_list(n):
        return [i**2 for i in range(n)]

    n = 1000
    gen = squares_generator(n)
    lst = squares_list(n)

    print(f"Generator size: {sys.getsizeof(gen)} bytes")
    print(f"List size: {sys.getsizeof(lst)} bytes")

memory_usage_demo()
```

---

## Real-World Example: Data Processing

```python
def process_sales_data(sales_file):
    """
    Process sales data from CSV file.

    Returns summary statistics.
    """
    import csv
    from collections import defaultdict

    sales_by_region = defaultdict(list)

    try:
        with open(sales_file, 'r') as file:
            reader = csv.DictReader(file)
            for row in reader:
                region = row['region']
                amount = float(row['amount'])
                sales_by_region[region].append(amount)

        # Calculate statistics
        summary = {}
        for region, amounts in sales_by_region.items():
            summary[region] = {
                'total': sum(amounts),
                'average': sum(amounts) / len(amounts),
                'count': len(amounts)
            }

        return summary

    except FileNotFoundError:
        print(f"File {sales_file} not found")
        return {}
    except Exception as e:
        print(f"Error processing file: {e}")
        return {}
```

---

## Function Documentation Standards

```python
def calculate_compound_interest(
    principal: float,
    rate: float,
    time: float,
    compound_frequency: int = 1
) -> float:
    """
    Calculate compound interest.

    This function calculates the final amount after compound interest
    is applied to a principal amount over a specified time period.

    Args:
        principal: The initial amount of money (must be positive)
        rate: Annual interest rate as decimal (e.g., 0.05 for 5%)
        time: Time period in years (must be positive)
        compound_frequency: Number of times interest compounds per year

    Returns:
        The final amount after compound interest

    Raises:
        ValueError: If any parameter is negative or zero where inappropriate
        TypeError: If parameters are not numeric

    Examples:
        >>> calculate_compound_interest(1000, 0.05, 2)
        1102.5
        >>> calculate_compound_interest(1000, 0.05, 2, 12)
        1104.89

    Note:
        Uses the formula: A = P(1 + r/n)^(nt)
        where A = final amount, P = principal, r = rate,
        n = compound frequency, t = time
    """
    # Validation
    if not all(isinstance(x, (int, float)) for x in [principal, rate, time]):
        raise TypeError("All parameters must be numeric")

    if principal <= 0:
        raise ValueError("Principal must be positive")

    if rate < 0:
        raise ValueError("Interest rate cannot be negative")

    if time <= 0:
        raise ValueError("Time must be positive")

    if compound_frequency <= 0:
        raise ValueError("Compound frequency must be positive")

    # Calculate compound interest
    amount = principal * (1 + rate / compound_frequency) ** (compound_frequency * time)
    return round(amount, 2)
```

---

## Advanced Error Handling Patterns

```python
from typing import Union, Tuple
from enum import Enum

class ResultStatus(Enum):
    SUCCESS = "success"
    ERROR = "error"

def safe_operation(x: float, y: float) -> Tuple[ResultStatus, Union[float, str]]:
    """
    Perform operation with explicit error handling.

    Returns tuple of (status, result_or_error_message)
    """
    try:
        if y == 0:
            return ResultStatus.ERROR, "Division by zero"

        result = x / y
        return ResultStatus.SUCCESS, result

    except TypeError:
        return ResultStatus.ERROR, "Invalid input types"
    except Exception as e:
        return ResultStatus.ERROR, f"Unexpected error: {str(e)}"

# Usage
status, result = safe_operation(10, 2)
if status == ResultStatus.SUCCESS:
    print(f"Result: {result}")
else:
    print(f"Error: {result}")
```

---

## Context Managers and Functions

```python
from contextlib import contextmanager

@contextmanager
def timer_context(operation_name):
    """Context manager for timing operations"""
    import time

    print(f"Starting {operation_name}")
    start_time = time.time()

    try:
        yield
    finally:
        end_time = time.time()
        print(f"{operation_name} completed in {end_time - start_time:.4f} seconds")

# Usage
with timer_context("Data Processing"):
    # Simulate some work
    import time
    time.sleep(1)
    result = sum(range(1000000))
```

---

## Function Factories

```python
def create_validator(min_val, max_val):
    """Factory function that creates validator functions"""

    def validator(value):
        """Validate that value is within specified range"""
        if not isinstance(value, (int, float)):
            raise TypeError("Value must be a number")

        if value < min_val:
            raise ValueError(f"Value {value} is below minimum {min_val}")

        if value > max_val:
            raise ValueError(f"Value {value} is above maximum {max_val}")

        return True

    # Add metadata to the created function
    validator.min_val = min_val
    validator.max_val = max_val
    validator.__name__ = f"validate_{min_val}_to_{max_val}"

    return validator

# Create specific validators
age_validator = create_validator(0, 150)
percentage_validator = create_validator(0, 100)

# Usage
try:
    age_validator(25)  # Valid
    percentage_validator(150)  # Raises ValueError
except ValueError as e:
    print(f"Validation error: {e}")
```

---

## Final Project Exercise

**Create a Library Management System with functions for:**

1. Adding books to inventory
2. Searching books by title/author
3. Checking out books
4. Returning books
5. Generating reports

**Requirements:**
- Use proper error handling
- Include comprehensive docstrings
- Implement data validation
- Use type hints
- Create unit tests

---

## Summary and Best Practices

**Key Takeaways:**
1. Functions are fundamental building blocks
2. Write small, focused, testable functions
3. Use meaningful names and documentation
4. Handle errors gracefully
5. Consider performance and memory usage
6. Follow Python conventions (PEP 8)
7. Test your functions thoroughly

---

## Questions?

**Thank you for your attention!**

*Remember: Functions are the building blocks of good Python programs.*

**Next Steps:**
- Practice with real projects
- Explore advanced topics (async, decorators)
- Study open-source Python code
- Build your own function library
