---
marp: true
theme: default
paginate: true
header: 'Python Functions - CS Course'
footer: 'University Computer Science Department'
---

# Python Functions
## A Comprehensive Guide for CS Students

---

## What is a Function?

A **function** is a reusable block of code that performs a specific task.

**Benefits:**
- Code reusability
- Better organization
- Easier debugging
- Modular programming

---

## Basic Function Syntax

```python
def function_name(parameters):
    """Optional docstring"""
    # Function body
    return value  # Optional
```

**Key components:**
- `def` keyword
- Function name
- Parameters (optional)
- Docstring (optional but recommended)
- Function body
- Return statement (optional)

---

## Simple Function Example

```python
def greet():
    """A simple greeting function"""
    print("Hello, <PERSON>!")

# Function call
greet()
```

**Output:**
```
Hello, <PERSON>!
```

---

## Functions with Parameters

```python
def greet_person(name):
    """Greet a specific person"""
    print(f"Hello, {name}!")

# Function calls
greet_person("<PERSON>")
greet_person("<PERSON>")
```

**Output:**
```
Hello, <PERSON>!
Hello, <PERSON>!
```

---

## Multiple Parameters

```python
def add_numbers(a, b):
    """Add two numbers together"""
    result = a + b
    print(f"{a} + {b} = {result}")

# Function call
add_numbers(5, 3)
add_numbers(10, 7)
```

**Output:**
```
5 + 3 = 8
10 + 7 = 17
```

---

## Return Values

```python
def multiply(x, y):
    """Multiply two numbers and return result"""
    return x * y

# Using return value
result = multiply(4, 6)
print(f"Result: {result}")

# Direct use in expression
total = multiply(3, 5) + multiply(2, 4)
print(f"Total: {total}")
```

**Output:**
```
Result: 24
Total: 23
```

---

## Default Parameters

```python
def introduce(name, age=18, city="Unknown"):
    """Introduce a person with default values"""
    print(f"Name: {name}")
    print(f"Age: {age}")
    print(f"City: {city}")

# Different ways to call
introduce("Alice")
introduce("Bob", 25)
introduce("Charlie", 30, "New York")
```

---

## Keyword Arguments

```python
def create_profile(name, age, email, city):
    """Create user profile"""
    return {
        'name': name,
        'age': age,
        'email': email,
        'city': city
    }

# Using keyword arguments
profile = create_profile(
    name="Alice",
    city="Boston",
    age=25,
    email="<EMAIL>"
)
```

---

## Variable-Length Arguments (*args)

```python
def sum_all(*numbers):
    """Sum any number of arguments"""
    total = 0
    for num in numbers:
        total += num
    return total

# Different number of arguments
print(sum_all(1, 2, 3))           # 6
print(sum_all(1, 2, 3, 4, 5))     # 15
print(sum_all(10))                # 10
```

---

## Keyword Variable Arguments (**kwargs)

```python
def print_info(**info):
    """Print key-value information"""
    for key, value in info.items():
        print(f"{key}: {value}")

# Using **kwargs
print_info(
    name="Alice",
    age=25,
    job="Engineer",
    city="Boston"
)
```

---

## Combining *args and **kwargs

```python
def flexible_function(required, *args, **kwargs):
    """Function with all parameter types"""
    print(f"Required: {required}")
    
    if args:
        print(f"Args: {args}")
    
    if kwargs:
        print(f"Kwargs: {kwargs}")

# Example call
flexible_function("Hello", 1, 2, 3, name="Alice", age=25)
```

---

## Local vs Global Scope

```python
global_var = "I'm global"

def scope_example():
    local_var = "I'm local"
    print(f"Inside function: {global_var}")
    print(f"Inside function: {local_var}")

scope_example()
print(f"Outside function: {global_var}")
# print(local_var)  # This would cause an error
```

---

## Global Keyword

```python
counter = 0  # Global variable

def increment():
    global counter
    counter += 1
    print(f"Counter: {counter}")

increment()  # Counter: 1
increment()  # Counter: 2
print(f"Final counter: {counter}")  # Final counter: 2
```

---

## Lambda Functions

```python
# Regular function
def square(x):
    return x ** 2

# Lambda equivalent
square_lambda = lambda x: x ** 2

# Usage
print(square(5))        # 25
print(square_lambda(5)) # 25

# Lambda with multiple parameters
add = lambda x, y: x + y
print(add(3, 4))        # 7
```

---

## Lambda with Built-in Functions

```python
numbers = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10]

# Filter even numbers
evens = list(filter(lambda x: x % 2 == 0, numbers))
print(f"Even numbers: {evens}")

# Square all numbers
squares = list(map(lambda x: x ** 2, numbers))
print(f"Squares: {squares[:5]}...")  # Show first 5
```

---

## Recursive Functions

```python
def factorial(n):
    """Calculate factorial recursively"""
    # Base case
    if n == 0 or n == 1:
        return 1
    
    # Recursive case
    return n * factorial(n - 1)

# Examples
print(f"5! = {factorial(5)}")   # 120
print(f"3! = {factorial(3)}")   # 6
```

---

## Fibonacci with Recursion

```python
def fibonacci(n):
    """Generate nth Fibonacci number"""
    if n <= 1:
        return n
    
    return fibonacci(n - 1) + fibonacci(n - 2)

# Generate first 10 Fibonacci numbers
fib_sequence = [fibonacci(i) for i in range(10)]
print(f"Fibonacci: {fib_sequence}")
```

---

## Docstrings and Documentation

```python
def calculate_area(length, width):
    """
    Calculate the area of a rectangle.
    
    Args:
        length (float): The length of the rectangle
        width (float): The width of the rectangle
    
    Returns:
        float: The area of the rectangle
    
    Example:
        >>> calculate_area(5, 3)
        15
    """
    return length * width
```

---

## Function Annotations

```python
def greet_user(name: str, age: int) -> str:
    """
    Greet a user with type hints.
    
    Args:
        name: User's name
        age: User's age
    
    Returns:
        Greeting message
    """
    return f"Hello {name}, you are {age} years old!"

# Usage
message = greet_user("Alice", 25)
print(message)
```

---

## Higher-Order Functions

```python
def apply_operation(numbers, operation):
    """Apply an operation to a list of numbers"""
    return [operation(num) for num in numbers]

def double(x):
    return x * 2

def square(x):
    return x ** 2

# Usage
nums = [1, 2, 3, 4, 5]
doubled = apply_operation(nums, double)
squared = apply_operation(nums, square)

print(f"Doubled: {doubled}")
print(f"Squared: {squared}")
```

---

## Decorators Introduction

```python
def my_decorator(func):
    """A simple decorator"""
    def wrapper():
        print("Before function call")
        func()
        print("After function call")
    return wrapper

@my_decorator
def say_hello():
    print("Hello!")

# Usage
say_hello()
```

**Output:**
```
Before function call
Hello!
After function call
```

---

## Best Practices

1. **Use descriptive names**
   ```python
   # Good
   def calculate_tax(income, rate):
       return income * rate
   
   # Avoid
   def calc(x, y):
       return x * y
   ```

2. **Keep functions small and focused**
3. **Use docstrings**
4. **Handle edge cases**
5. **Use type hints when helpful**

---

## Common Mistakes to Avoid

1. **Modifying mutable default arguments**
   ```python
   # Wrong
   def add_item(item, target_list=[]):
       target_list.append(item)
       return target_list
   
   # Correct
   def add_item(item, target_list=None):
       if target_list is None:
           target_list = []
       target_list.append(item)
       return target_list
   ```

---

## Practice Exercise

**Create a function that:**
1. Takes a list of numbers
2. Filters out negative numbers
3. Squares the remaining numbers
4. Returns the sum

```python
def process_numbers(numbers):
    """Your implementation here"""
    pass

# Test with: [1, -2, 3, -4, 5]
# Expected result: 1² + 3² + 5² = 35
```

---

## Summary

**Key Concepts Covered:**
- Function definition and calling
- Parameters and arguments
- Return values
- Scope and global variables
- Lambda functions
- Recursion
- Documentation and type hints
- Best practices

**Next Steps:**
- Practice writing functions
- Explore advanced topics (decorators, generators)
- Apply functions in real projects

---

## Questions?

**Thank you for your attention!**

*Remember: Functions are the building blocks of good Python programs.*
